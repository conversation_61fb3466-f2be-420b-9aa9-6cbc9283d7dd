"use server";
import React from "react";
import Container from "@/components/core/container";
import ProjectHeading from "@/components/pages/projects/project-heading";
import ProjectsList from "@/components/pages/projects/projects-list";
import db from "@/lib/db";

type Props = {};

const ProjectsPage = async (props: Props) => {
  const projects = await db.project.findMany({
    where: {
      userId: "343b31c8-a6de-4657-8180-565f3f79030b",
    },
    include: {
      user: true,
    },
    orderBy: {
      createdAt: "desc",
    },
  });

  return (
    <Container className="space-y-10">
      <ProjectHeading />
      <ProjectsList projects={projects} />
    </Container>
  );
};

export default ProjectsPage;
