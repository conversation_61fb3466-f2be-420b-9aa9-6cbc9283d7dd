import React from "react";
import Logo from "./logo";
import { <PERSON><PERSON> } from "../ui/button";
import Link from "next/link";
import Container from "../core/container";

type Props = {};

const Header = (props: Props) => {
  return (
    <header className="sticky top-0 z-50 bg-background">
      <Container className="flex justify-between items-center">
        <Logo />
        <Button asChild>
          <Link href="/project">Launch</Link>
        </Button>
      </Container>
    </header>
  );
};

export default Header;
