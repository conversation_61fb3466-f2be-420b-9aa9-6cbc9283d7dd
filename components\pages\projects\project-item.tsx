import React from "react";
import { Card } from "@/components/ui/card";
import Image from "next/image";
import Placeholder from "@/public/placeholder1.png";
import { Project } from "@prisma/client";
import ProjectTitleUpdateForm from "./project-title-update-form";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Ellipsis, Trash2Icon } from "lucide-react";
import { deleteProjectAction } from "@/actions/projects";

type Props = {
  project: Project;
};

const ProjectItem = ({ project: { id, title } }: Props) => {
  const handleDeleteProject = async (id: string) => {
    try {
      await deleteProjectAction(id);
    } catch (error) {
      console.error("Error deleting project:", error);
    }
  };
  return (
    <li className="space-y-4 relative">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button className="absolute top-4 right-4">
            <Ellipsis className="size-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="bg-primary" align="end">
          <DropdownMenuItem asChild>
            <Dialog>
              <DialogTrigger asChild>
                <Button className="w-full">
                  <Trash2Icon className="size-4" />
                  <span>Delete</span>
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Are you absolutely sure?</DialogTitle>
                  <DialogDescription>
                    This action cannot be undone. This will permanently delete
                    your account and remove your data from our servers.
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                  <DialogClose asChild>
                    <Button variant="secondary">Cancel</Button>
                  </DialogClose>
                  <Button variant="destructive">Yes, Delete</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <Card className="p-0 mb-8">
        <Image src={Placeholder} alt="project" />
      </Card>
      <ProjectTitleUpdateForm title={title} id={id} />
    </li>
  );
};

export default ProjectItem;
