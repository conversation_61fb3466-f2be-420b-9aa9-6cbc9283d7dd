"use client";

import React, { ChangeEvent, useCallback, useEffect, useState } from "react";
import Form from "next/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Project } from "@prisma/client";
import useDebounce from "@/hooks/use-debounce";
import {
  updateProjectTitleAction,
  UpdateProjectTitleProps,
} from "@/actions/projects";
import { toast } from "sonner";

type Props = {
  title: Project["title"];
  id: Project["id"];
};

const ProjectTitleUpdateForm = ({ title, id }: Props) => {
  const [value, setValue] = useState(title);

  const debounced = useDebounce(value, 500);

  const handleValueChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setValue(value);
  };

  const updateProjectTitleCallback = useCallback(
    async ({ title, id }: UpdateProjectTitleProps) => {
      const { data, message, error } = await updateProjectTitleAction({
        title,
        id,
      });

      if (data) {
        toast.success(message);
      }

      if (error) {
        toast.error(message);
      }
    },
    []
  );

  useEffect(() => {
    if (title !== debounced) {
      updateProjectTitleCallback({ title: debounced, id });
    }
  }, [debounced, id, updateProjectTitleCallback]);

  return (
    <>
      <Form action="/project" className="relative">
        <Input
          type="text"
          id="project-title"
          placeholder="Project Title"
          className="peer text-xs"
          value={value}
          onChange={handleValueChange}
        />
        <Label
          htmlFor="project-title"
          className="peer-placeholder-shown:sr-only peer-focus:not-sr-only peer-focus:absolute peer-focus:px-1 absolute -top-2 left-2 text-xs bg-background px-1"
        >
          Project Title
        </Label>
      </Form>
    </>
  );
};

export default ProjectTitleUpdateForm;
