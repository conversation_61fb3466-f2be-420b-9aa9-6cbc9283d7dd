import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  try {
    // Clear existing data (optional - remove if you want to keep existing data)
    await prisma.project.deleteMany();
    await prisma.user.deleteMany();

    // Create a user
    const user = await prisma.user.create({
      data: {
        name: "Manish Batra",
        email: "<EMAIL>",
      },
    });

    console.log("Created user:", user);

    // Create projects for the user
    const projects = await prisma.project.createMany({
      data: [
        {
          title: "My First Project",
          userId: user.id,
        },
        {
          title: "My Second Project",
          userId: user.id,
        },
        {
          title: "My Third Project",
          userId: user.id,
        },
        {
          title: "My Fourth Project",
          userId: user.id,
        },
      ],
    });

    console.log(`Created ${projects.count} projects`);

    // Verify the data was created
    const userWithProjects = await prisma.user.findUnique({
      where: { id: user.id },
      include: { Project: true },
    });

    console.log(
      "User with projects:",
      JSON.stringify(userWithProjects, null, 2)
    );
    console.log("Seeding completed successfully!");
  } catch (error) {
    console.error("Error during seeding:", error);
    throw error;
  }
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error("Seeding failed:", e);
    await prisma.$disconnect();
    process.exit(1);
  });
